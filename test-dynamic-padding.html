<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Sales Card Padding Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            margin: 20px;
            font-family: 'Amazon Ember', sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        
        .test-description {
            margin-bottom: 30px;
            padding: 20px;
            background: #fff;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .test-controls {
            margin-bottom: 30px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        
        .test-btn {
            margin: 5px;
            padding: 8px 16px;
            background: #470CED;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-btn:hover {
            background: #3a0bc4;
        }
        
        .test-item {
            padding: 12px;
            margin: 8px 0;
            background: #f9f9f9;
            border-radius: 6px;
            border-left: 3px solid #606F95;
            font-size: 13px;
        }
        
        .padding-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">Dynamic Sales Card Padding Test</div>
        
        <div class="test-description">
            <strong>Test Objective:</strong><br>
            • Verify that Today's and Yesterday's sales cards automatically adjust right padding based on scrollbar visibility<br>
            • <strong>24px right padding</strong> when scrollbar is hidden (content fits)<br>
            • <strong>12px right padding</strong> when scrollbar is visible (content overflows)<br>
            • Only affects Today's and Yesterday's cards, not other sales cards<br><br>
            
            <strong>How to Test:</strong><br>
            1. Use the buttons below to add/remove content<br>
            2. Watch the padding indicator in the top-right corner<br>
            3. Observe how the right padding changes automatically<br>
            4. Verify scrollbar appears/disappears as expected
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="addContent()">Add Content (Trigger Scrollbar)</button>
            <button class="test-btn" onclick="removeContent()">Remove Content (Hide Scrollbar)</button>
            <button class="test-btn" onclick="resetContent()">Reset to Default</button>
            <button class="test-btn" onclick="checkPadding()">Check Current Padding</button>
        </div>
        
        <div class="padding-indicator" id="paddingIndicator">
            Padding Status: Checking...
        </div>
        
        <!-- Sales Cards Container (mimicking the real structure) -->
        <div class="sales-cards-container">
            <div class="sales-cards-row">
                <!-- Today's Sales Card -->
                <div class="Sales-card-div" id="todaysCard">
                    <div class="Sales-title-date-div">
                        <span class="sales-card-title">Today's Sales</span>
                        <span class="sales-card-date">Test Date</span>
                    </div>
                    
                    <div class="sales-scrollable-content" id="todaysContent">
                        <div class="test-item">Item 1 - Default content</div>
                        <div class="test-item">Item 2 - Default content</div>
                        <div class="test-item">Item 3 - Default content</div>
                        <div class="test-item">Item 4 - Default content</div>
                        <div class="test-item">Item 5 - Default content</div>
                    </div>
                </div>
                
                <!-- Yesterday's Sales Card -->
                <div class="Sales-card-div" id="yesterdaysCard">
                    <div class="Sales-title-date-div">
                        <span class="sales-card-title">Yesterday's Sales</span>
                        <span class="sales-card-date">Test Date</span>
                    </div>
                    
                    <div class="sales-scrollable-content" id="yesterdaysContent">
                        <div class="test-item">Item 1 - Default content</div>
                        <div class="test-item">Item 2 - Default content</div>
                        <div class="test-item">Item 3 - Default content</div>
                        <div class="test-item">Item 4 - Default content</div>
                        <div class="test-item">Item 5 - Default content</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Include the dynamic padding functions (simplified version for testing)
        function updateSalesCardPadding(salesCard) {
            const scrollableContent = salesCard.querySelector('.sales-scrollable-content');
            if (!scrollableContent) return;
            
            const hasScrollbar = scrollableContent.scrollHeight > scrollableContent.clientHeight;
            
            if (hasScrollbar) {
                salesCard.classList.add('scrollbar-visible');
                salesCard.classList.remove('scrollbar-hidden');
            } else {
                salesCard.classList.add('scrollbar-hidden');
                salesCard.classList.remove('scrollbar-visible');
            }
            
            updatePaddingIndicator();
        }
        
        function updateAllSalesCardsPadding() {
            const todayYesterdayCards = document.querySelectorAll('.sales-cards-container .sales-cards-row .Sales-card-div');
            todayYesterdayCards.forEach(updateSalesCardPadding);
        }
        
        function updatePaddingIndicator() {
            const todaysCard = document.getElementById('todaysCard');
            const yesterdaysCard = document.getElementById('yesterdaysCard');
            const indicator = document.getElementById('paddingIndicator');
            
            const todaysHasScrollbar = todaysCard.classList.contains('scrollbar-visible');
            const yesterdaysHasScrollbar = yesterdaysCard.classList.contains('scrollbar-visible');
            
            const todaysPadding = todaysHasScrollbar ? '12px' : '24px';
            const yesterdaysPadding = yesterdaysHasScrollbar ? '12px' : '24px';
            
            indicator.innerHTML = `
                Today's: ${todaysPadding} right padding ${todaysHasScrollbar ? '(scrollbar visible)' : '(scrollbar hidden)'}<br>
                Yesterday's: ${yesterdaysPadding} right padding ${yesterdaysHasScrollbar ? '(scrollbar visible)' : '(scrollbar hidden)'}
            `;
        }
        
        // Test functions
        function addContent() {
            const contents = [document.getElementById('todaysContent'), document.getElementById('yesterdaysContent')];
            contents.forEach(content => {
                for (let i = 0; i < 10; i++) {
                    const item = document.createElement('div');
                    item.className = 'test-item';
                    item.textContent = `Added Item ${content.children.length + 1} - This is additional content to trigger scrollbar`;
                    content.appendChild(item);
                }
            });
            setTimeout(updateAllSalesCardsPadding, 50);
        }
        
        function removeContent() {
            const contents = [document.getElementById('todaysContent'), document.getElementById('yesterdaysContent')];
            contents.forEach(content => {
                const items = content.querySelectorAll('.test-item');
                for (let i = items.length - 1; i >= 5; i--) {
                    if (items[i]) items[i].remove();
                }
            });
            setTimeout(updateAllSalesCardsPadding, 50);
        }
        
        function resetContent() {
            const contents = [document.getElementById('todaysContent'), document.getElementById('yesterdaysContent')];
            contents.forEach(content => {
                content.innerHTML = '';
                for (let i = 1; i <= 5; i++) {
                    const item = document.createElement('div');
                    item.className = 'test-item';
                    item.textContent = `Item ${i} - Default content`;
                    content.appendChild(item);
                }
            });
            setTimeout(updateAllSalesCardsPadding, 50);
        }
        
        function checkPadding() {
            updateAllSalesCardsPadding();
            alert('Padding status updated! Check the indicator in the top-right corner.');
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                updateAllSalesCardsPadding();
                console.log('Dynamic padding test initialized');
            }, 100);
        });
        
        // Update on window resize
        window.addEventListener('resize', () => {
            setTimeout(updateAllSalesCardsPadding, 100);
        });
    </script>
</body>
</html>
